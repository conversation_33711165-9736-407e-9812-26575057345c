 # Importing Python Packages 

!pip install google-cloud-storage google-cloud-documentai google-cloud-aiplatform pandas openpyxl requests tqdm vertexai  --quiet
!pip install --upgrade google-cloud-aiplatform  --quiet

# Enabling the Vertex AI API
!gcloud services enable aiplatform.googleapis.com  --quiet


# Importing Python Libraries
import os
import pandas as pd
import requests
from google.cloud import storage
from google.cloud import documentai_v1 as documentai
from google.cloud import aiplatform
from io import BytesIO
import tempfile
import time
from tqdm import tqdm
import logging
from datetime import datetime
import concurrent.futures
import threading
from functools import partial

# Configuration
PROJECT_ID = "avy-lab-ai"  # GCP project ID
DOCAI_LOCATION = "us"  # Location for Document AI
VERTEX_LOCATION = "us-central1"  # Location for Vertex AI  
BUCKET_NAME = "dice-pfas"
EXCEL_PATH = "SDS Full Export_050625.xlsx"
OUTPUT_PATH = "SDS Full Export_050625_updated.xlsx"
PROCESSOR_ID = "c467fcb2578b7e47"  #  Document AI processor ID
ENABLE_COST_TRACKING = False  # Set to False to disable cost analysis


 
# Initialize clients
storage_client = storage.Client()
docai_client = documentai.DocumentProcessorServiceClient()

# Setup logging
def setup_logging():
    log_filename = f"pfas_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return log_filename

# Initialize logging
log_file = setup_logging()
logger = logging.getLogger(__name__)

# Function to download file from GCS
def download_from_gcs(bucket_name, source_blob_name):
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(source_blob_name)
    data = BytesIO()
    blob.download_to_file(data)
    data.seek(0)
    return data

# Function to upload file to GCS
def upload_to_gcs(bucket_name, destination_blob_name, data):
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_file(data)

# Function to process document with Document AI
def process_document(content, mime_type="application/pdf"):
    import re  
    name = f"projects/{PROJECT_ID}/locations/{DOCAI_LOCATION}/processors/{PROCESSOR_ID}"
    
    # Configure the process request with imageless mode enabled from start
    document = documentai.RawDocument(content=content, mime_type=mime_type)
    
    # First try with imageless mode for up to 30 pages
    request = documentai.ProcessRequest(
        name=name,
        raw_document=document,
        imageless_mode=True,
        process_options=documentai.ProcessOptions(
            ocr_config=documentai.OcrConfig(
                enable_image_quality_scores=False,
                enable_native_pdf_parsing=True,
                enable_symbol=False
            )
        )
    )
    
    try:
        result = docai_client.process_document(request=request)
        pages_processed = len(result.document.pages) if result.document.pages else 1
        logger.debug(f"Document processed successfully with {pages_processed} pages (imageless mode)")
        return result.document, pages_processed
    except Exception as e:
        if "PAGE_LIMIT_EXCEEDED" in str(e):
            # Extract page count from error message
            pages_match = re.search(r'got (\d+)', str(e))
            total_pages = int(pages_match.group(1)) if pages_match else "unknown"
            logger.warning(f"Document has {total_pages} pages, processing first 30 pages only")
            
            # For very large documents, explicitly limit to first 30 pages with imageless mode
            request = documentai.ProcessRequest(
                name=name,
                raw_document=document,
                imageless_mode=True,
                process_options=documentai.ProcessOptions(
                    individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                        pages=list(range(1, 31))  # Process pages 1-30
                    ),
                    ocr_config=documentai.OcrConfig(
                        enable_image_quality_scores=False,
                        enable_native_pdf_parsing=True,
                        enable_symbol=False
                    )
                )
            )
            try:
                result = docai_client.process_document(request=request)
                logger.debug("Document processed with 30-page limit (imageless mode)")
                return result.document, 30
            except Exception as retry_error:
                logger.error(f"Failed with 30-page limit: {str(retry_error)}")
                # Final fallback: try first 15 pages without imageless mode
                request = documentai.ProcessRequest(
                    name=name,
                    raw_document=document,
                    process_options=documentai.ProcessOptions(
                        individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                            pages=list(range(1, 16))
                        ),
                        ocr_config=documentai.OcrConfig(
                            enable_image_quality_scores=False,
                            enable_native_pdf_parsing=True,
                            enable_symbol=False
                        )
                    )
                )
                result = docai_client.process_document(request=request)
                logger.debug("Document processed with 15-page limit (fallback mode)")
                return result.document, 15
        else:
            logger.error(f"Document AI processing failed: {str(e)}")
            raise e

# Function to analyze text for PFAS chemicals using Google's Gemini model
def analyze_for_pfas(text):
    import json
    import re
    from vertexai.generative_models import GenerativeModel
    import vertexai
    
    # Initialize Vertex AI with project and location
    vertexai.init(project=PROJECT_ID, location=VERTEX_LOCATION)
    
    # Create prompt for the model with stricter JSON formatting
    prompt = f"""
    You are a chemical expert specializing in PFAS (Perfluoroalkyl and Polyfluoroalkyl Substances).
    Analyze the following Safety Data Sheet (SDS) text and determine:
    1. Is any PFAS chemical present? (Yes/No)
    2. If yes, what is the specific PFAS chemical name?
    3. What is the supplier name?
    4. What are the typical industrial uses/applications of this PFAS chemical?
    
    IMPORTANT: Respond ONLY with valid JSON in this exact format:
    {{
        "pfas_present": "Yes" or "No",
        "pfas_chemical_name": "chemical name or N/A",
        "supplier_name": "supplier name or N/A", 
        "pfas_uses": "uses description or N/A"
    }}
    
    Do not include any text before or after the JSON.
    
    SDS Text:
    {text[:15000]}
    """
    
    try:
        # Load Gemini Pro model
        model = GenerativeModel("gemini-2.5-pro")
        
        # Generate content
        response = model.generate_content(prompt)
        logger.debug(f"Gemini API response received")
        
        # Clean and extract JSON from response
        text_response = response.text.strip()
        
        # Remove markdown code blocks if present
        text_response = re.sub(r'```json\s*', '', text_response)
        text_response = re.sub(r'```\s*$', '', text_response)
        
        # Try to parse as JSON
        try:
            result = json.loads(text_response)
            logger.debug(f"Successfully parsed JSON response")
            return result
            
        except json.JSONDecodeError:
            logger.warning("Failed to parse JSON response, attempting extraction")
            # Extract JSON-like content using regex
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', text_response, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group(0))
                    logger.debug(f"Successfully extracted and parsed JSON")
                    return result
                except json.JSONDecodeError:
                    pass
            
            # If JSON extraction fails, parse manually
            result = {
                "pfas_present": "Unknown",
                "pfas_chemical_name": "Could not determine",
                "supplier_name": "Could not determine",
                "pfas_uses": "Could not determine"
            }
            
            # Try to extract information from the text
            if re.search(r'"pfas_present":\s*"Yes"', text_response, re.IGNORECASE):
                result["pfas_present"] = "Yes"
            elif re.search(r'"pfas_present":\s*"No"', text_response, re.IGNORECASE):
                result["pfas_present"] = "No"
            elif "yes" in text_response.lower() and "pfas" in text_response.lower():
                result["pfas_present"] = "Yes"
            elif "no" in text_response.lower() and "pfas" in text_response.lower():
                result["pfas_present"] = "No"
            
            # Extract chemical name
            chemical_match = re.search(r'"pfas_chemical_name":\s*"([^"]+)"', text_response, re.IGNORECASE)
            if chemical_match:
                result["pfas_chemical_name"] = chemical_match.group(1).strip()
            
            # Extract supplier name
            supplier_match = re.search(r'"supplier_name":\s*"([^"]+)"', text_response, re.IGNORECASE)
            if supplier_match:
                result["supplier_name"] = supplier_match.group(1).strip()
            
            # Extract uses
            uses_match = re.search(r'"pfas_uses":\s*"([^"]+)"', text_response, re.IGNORECASE)
            if uses_match:
                result["pfas_uses"] = uses_match.group(1).strip()
            
            logger.debug(f"Manually parsed response: {result}")
            return result
        
    except Exception as e:
        logger.error(f"Gemini API Error: {str(e)}")
        return {
            "pfas_present": "Error",
            "pfas_chemical_name": f"Error: {str(e)}",
            "supplier_name": "Error",
            "pfas_uses": "Error"
        }

# Add thread-safe statistics tracking
class ThreadSafeStats:
    def __init__(self):
        self.lock = threading.Lock()
        self.stats = {
            'total_rows': 0,
            'processed': 0,
            'pfas_found': 0,
            'errors': 0,
            'skipped': 0,
            'download_errors': 0,
            'doc_processing_errors': 0,
            'api_errors': 0,
            'docai_pages_processed': 0,
            'gemini_requests': 0,
            'total_text_chars': 0
        }
    
    def increment(self, key, value=1):
        with self.lock:
            self.stats[key] += value
    
    def get_stats(self):
        with self.lock:
            return self.stats.copy()

# Process single row function
def process_single_row(row_data, stats_tracker, cost_rates):
    idx, row = row_data
    
    if pd.isna(row['Hyperlinks']):
        stats_tracker.increment('skipped')
        logger.debug(f"Row {idx}: Skipped - no hyperlink")
        return idx, None, None
    
    hyperlink = row['Hyperlinks']
    logger.info(f"Row {idx}: Processing {hyperlink}")
    
    result_data = {}
    error_msg = None
    
    try:
        # Download SDS from hyperlink
        response = requests.get(hyperlink, timeout=30)
        
        if response.status_code == 200:
            logger.debug(f"Row {idx}: Successfully downloaded PDF ({len(response.content)} bytes)")
            
            # Save content to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(response.content)
                temp_path = temp_file.name
            
            try:
                # Process with Document AI
                with open(temp_path, "rb") as f:
                    content = f.read()
                document, pages_processed = process_document(content)
                if ENABLE_COST_TRACKING:
                    stats_tracker.increment('docai_pages_processed', pages_processed)
                logger.debug(f"Row {idx}: Document AI processing successful ({pages_processed} pages)")
                
                # Analyze text for PFAS
                if ENABLE_COST_TRACKING:
                    text_length = len(document.text)
                    stats_tracker.increment('total_text_chars', text_length)
                    stats_tracker.increment('gemini_requests')
                
                result = analyze_for_pfas(document.text)
                logger.debug(f"Row {idx}: PFAS analysis result: {result}")
                
                # Prepare result data
                result_data = {
                    'PFAS_Present': result.get('pfas_present', 'Unknown'),
                    'PFAS_Chemical_Name': result.get('pfas_chemical_name', ''),
                    'Supplier_Name': result.get('supplier_name', ''),
                    'PFAS_Uses': result.get('pfas_uses', '')
                }
                
                stats_tracker.increment('processed')
                if result.get('pfas_present', '').lower() == 'yes':
                    stats_tracker.increment('pfas_found')
                    logger.info(f"Row {idx}: PFAS FOUND - {result.get('pfas_chemical_name', 'Unknown')}")
                
            except Exception as doc_error:
                stats_tracker.increment('doc_processing_errors')
                error_msg = f"Document processing error: {str(doc_error)}"
                logger.error(f"Row {idx}: {error_msg}")
            
            # Clean up
            os.unlink(temp_path)
            
        else:
            stats_tracker.increment('download_errors')
            error_msg = f"Failed to download: HTTP {response.status_code}"
            logger.error(f"Row {idx}: {error_msg}")
            
    except Exception as e:
        stats_tracker.increment('errors')
        error_msg = str(e)
        logger.error(f"Row {idx}: General error: {error_msg}")
    
    return idx, result_data, error_msg


 
# Main function with parallel processing
def analyze_sds_from_hyperlinks():
    start_time = datetime.now()
    logger.info(f"Starting PFAS analysis at {start_time}")
    
    # Thread-safe statistics tracking
    stats_tracker = ThreadSafeStats()
    
    # Cost estimates
    cost_rates = {
        'docai_per_page': 0.0015,
        'gemini_per_1k_chars': 0.000125,
        'storage_operations': 0.0004
    }
    
    # Download Excel file
    logger.info("Downloading Excel file...")
    excel_data = download_from_gcs(BUCKET_NAME, EXCEL_PATH)
    df = pd.read_excel(excel_data)
    stats_tracker.stats['total_rows'] = len(df)
    logger.info(f"Loaded Excel file with {stats_tracker.stats['total_rows']} rows")
    
    # Check if 'Hyperlinks' column exists
    if 'Hyperlinks' not in df.columns:
        logger.error("'Hyperlinks' column not found in Excel file")
        raise ValueError("'Hyperlinks' column not found in Excel file")
    
    # Add result columns if they don't exist
    for col in ['PFAS_Present', 'PFAS_Chemical_Name', 'Supplier_Name', 'PFAS_Uses', 'Error_Details']:
        if col not in df.columns:
            df[col] = None
    
    logger.info("Starting parallel document processing...")
    
    # Configure parallel processing
    MAX_WORKERS = 10  # Adjust based on API limits and system capacity
    
    # Prepare data for parallel processing
    row_data = [(idx, row) for idx, row in df.iterrows()]
    
    # Process in batches to manage memory and save progress
    batch_size = 50
    total_batches = (len(row_data) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(row_data))
        batch_data = row_data[start_idx:end_idx]
        
        logger.info(f"Processing batch {batch_num + 1}/{total_batches} (rows {start_idx}-{end_idx-1})")
        
        # Process batch in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all tasks
            future_to_idx = {
                executor.submit(process_single_row, row_item, stats_tracker, cost_rates): row_item[0] 
                for row_item in batch_data
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    row_idx, result_data, error_msg = future.result()
                    
                    # Update DataFrame with results
                    if result_data:
                        for key, value in result_data.items():
                            df.at[row_idx, key] = value
                    
                    if error_msg:
                        df.at[row_idx, 'Error_Details'] = error_msg
                        
                except Exception as exc:
                    logger.error(f"Row {idx} generated an exception: {exc}")
                    df.at[idx, 'Error_Details'] = f"Processing exception: {str(exc)}"
        
        # Save progress after each batch
        current_stats = stats_tracker.get_stats()
        logger.info(f"Batch {batch_num + 1} complete. Processed: {current_stats['processed']}, PFAS found: {current_stats['pfas_found']}")
        
        # Save intermediate results
        output_data = BytesIO()
        df.to_excel(output_data, index=False)
        output_data.seek(0)
        upload_to_gcs(BUCKET_NAME, OUTPUT_PATH, output_data)
    
    # Final statistics and cost calculation
    final_stats = stats_tracker.get_stats()
    
    if ENABLE_COST_TRACKING:
        estimated_costs = {
            'docai_cost': final_stats['docai_pages_processed'] * cost_rates['docai_per_page'],
            'gemini_cost': (final_stats['total_text_chars'] / 1000) * cost_rates['gemini_per_1k_chars'],
            'storage_cost': cost_rates['storage_operations'] * (total_batches + 2)
        }
        estimated_costs['total_cost'] = sum(estimated_costs.values())
    
    # Save final results
    output_data = BytesIO()
    df.to_excel(output_data, index=False)
    output_data.seek(0)
    upload_to_gcs(BUCKET_NAME, OUTPUT_PATH, output_data)
    
    # Final statistics
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("="*50)
    logger.info("PFAS ANALYSIS COMPLETE")
    logger.info("="*50)
    logger.info(f"Duration: {duration}")
    logger.info(f"Total rows: {final_stats['total_rows']}")
    logger.info(f"Successfully processed: {final_stats['processed']}")
    logger.info(f"PFAS chemicals found: {final_stats['pfas_found']}")
    logger.info(f"Success rate: {(final_stats['processed']/final_stats['total_rows']*100):.1f}%")
    
    if ENABLE_COST_TRACKING:
        logger.info(f"TOTAL ESTIMATED COST: ${estimated_costs['total_cost']:.4f}")
        print(f"Total estimated cost: ${estimated_costs['total_cost']:.4f}")
    
    print(f"Analysis complete. Results saved to gs://{BUCKET_NAME}/{OUTPUT_PATH}")

if __name__ == "__main__":
    analyze_sds_from_hyperlinks()
